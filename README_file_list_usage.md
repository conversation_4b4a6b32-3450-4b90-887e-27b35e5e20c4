# File List Usage for Layout Bricks Instructions Analyzer

## Overview

The Layout Bricks Instructions Analyzer has been updated to support processing files from a file list instead of scanning entire folders. This provides more precise control over which files to analyze.

## New Constants

- `DEFAULT_BASE_FOLDER`: Base folder path that will be combined with relative file paths
- `DEFAULT_FILE_LIST`: Default name for the file list (file_list.txt)

## New Command Line Arguments

- `--file-list`: Path to file containing list of relative file paths to analyze
- `--base-folder`: Base folder path to combine with relative file paths

## File List Format

Create a text file (e.g., `file_list.txt`) with one relative file path per line:

```
# Comments start with # and are ignored
subfolder1/document1.txt
subfolder2/document2.txt
notes/important_notes.txt
data/analysis_data.txt
```

## Usage Examples

### Basic usage with default file list:
```bash
python main.py
```
This uses `file_list.txt` and `DEFAULT_BASE_FOLDER`.

### Specify custom file list and base folder:
```bash
python main.py --file-list my_files.txt --base-folder "C:\MyDocuments"
```

### With additional options:
```bash
python main.py --file-list files_to_analyze.txt --base-folder "D:\TextFiles" --output-folder "D:\Results" --max-workers 8
```

## How It Works

1. The application reads the file list from the specified file
2. Each relative path is combined with the base folder to create full file paths
3. Files are processed using the same parallel processing and rate limiting as before
4. Missing files are logged as warnings but don't stop the analysis
5. Already processed files are skipped (same as before)

## Benefits

- **Precise Control**: Only analyze specific files you want
- **Flexible Paths**: Combine any base folder with relative paths
- **Comments Support**: Add comments to your file lists for organization
- **Error Handling**: Missing files are handled gracefully
- **Same Features**: All existing features (parallel processing, rate limiting, CSV output) still work

## Migration from Folder-based Processing

If you were previously using folder-based processing, you can:

1. Create a file list with all the files you want to analyze
2. Set the base folder to your previous input folder
3. Use the new command line arguments

The analyzer will process the same files but with more control over the selection.
